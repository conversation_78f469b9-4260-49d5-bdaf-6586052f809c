package io.daocloud.prometheustestdemo.controller;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureTestMvc
public class HelloControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Test
    public void testRateLimiting() throws Exception {
        // 发送大量请求测试限流
        int successCount = 0;
        int rateLimitCount = 0;
        
        for (int i = 0; i < 150; i++) {
            try {
                mockMvc.perform(get("/hello"))
                        .andExpect(status().isOk());
                successCount++;
            } catch (AssertionError e) {
                // 期望部分请求被限流
                rateLimitCount++;
            }
        }
        
        // 验证确实有请求被限流
        assert rateLimitCount > 0 : "Rate limiting should have occurred";
    }
}