package io.daocloud.prometheustestdemo.controller;

import io.github.bucket4j.Bandwidth;
import io.github.bucket4j.Bucket;
import io.github.bucket4j.Bucket4j;
import io.github.bucket4j.Refill;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

@RestController
@Slf4j
public class HelloController {
    
    private final Bucket bucket;
    
    public HelloController() {
        // 创建限流桶：每秒100个令牌，容量100
        Bandwidth limit = Bandwidth.classic(100, Refill.intervally(100, Duration.ofSeconds(1)));
        this.bucket = Bucket4j.builder()
                .addLimit(limit)
                .build();
    }
    
    @GetMapping("/hello")
    public ResponseEntity<Map<String, String>> hello() {
        // 尝试获取令牌
        if (bucket.tryConsume(1)) {
            Map<String, String> response = new HashMap<>();
            response.put("msg", "hello");
            log.info("Request processed successfully");
            return ResponseEntity.ok(response);
        } else {
            log.warn("Rate limit exceeded");
            return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
        }
    }
}